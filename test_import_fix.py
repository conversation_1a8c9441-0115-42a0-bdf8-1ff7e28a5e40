"""
Test script to verify that the circular import issue is fixed
"""

def test_imports():
    """Test all imports to ensure no circular dependencies"""
    print("Testing imports...")
    
    try:
        print("1. Testing main.py imports...")
        from student import Student
        print("   ✓ student.Student imported successfully")
        
        from train import Train
        print("   ✓ train.Train imported successfully")
        
        from face_recognition import Face_Recognition
        print("   ✓ face_recognition.Face_Recognition imported successfully")
        
        print("2. Testing face_recognition.py imports...")
        import face_recognition
        print("   ✓ face_recognition module imported successfully")
        
        print("3. Testing individual modules...")
        import cv2
        print(f"   ✓ OpenCV version: {cv2.__version__}")
        
        import numpy as np
        print(f"   ✓ NumPy version: {np.__version__}")
        
        from PIL import Image
        print("   ✓ PIL imported successfully")
        
        print("\n✅ All imports successful! Circular import issue is FIXED!")
        return True
        
    except ImportError as e:
        print(f"\n❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False

def test_main_execution():
    """Test that main.py can be executed without errors"""
    print("\nTesting main.py execution...")
    
    try:
        # Import the main class
        from main import Face_Recognition_System
        print("✓ Face_Recognition_System class imported successfully")
        
        # Test that we can create the class (without actually showing GUI)
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # This should work without circular import errors
        app = Face_Recognition_System(root)
        print("✓ Face_Recognition_System instance created successfully")
        
        root.destroy()
        print("✓ Main application test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Main execution test failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CIRCULAR IMPORT FIX VERIFICATION TEST")
    print("=" * 60)
    
    import_success = test_imports()
    main_success = test_main_execution()
    
    print("\n" + "=" * 60)
    if import_success and main_success:
        print("🎉 ALL TESTS PASSED! The circular import issue is FIXED!")
        print("\nYou can now run 'python main.py' without any import errors.")
    else:
        print("❌ Some tests failed. Please check the error messages above.")
    print("=" * 60)
