import cv2
import numpy as np
import os
from PIL import Image

def test_training_function():
    """Test the training function to identify issues"""
    print("Testing face recognition training function...")
    
    # Check if data directory exists
    data_dir = "data"
    if not os.path.exists(data_dir):
        print("✗ Data directory does not exist")
        return False
    
    # Get all image files
    try:
        path = [os.path.join(data_dir, file) for file in os.listdir(data_dir)]
        print(f"✓ Found {len(path)} files in data directory")
        
        if len(path) == 0:
            print("✗ No files found in data directory")
            return False
            
    except Exception as e:
        print(f"✗ Error reading data directory: {e}")
        return False
    
    faces = []
    ids = []
    
    print("Processing images...")
    
    for i, image_path in enumerate(path):
        try:
            print(f"Processing image {i+1}/{len(path)}: {os.path.basename(image_path)}")
            
            # Open and convert image to grayscale
            img = Image.open(image_path).convert('L')
            imageNp = np.array(img, 'uint8')
            
            # Extract ID from filename (format: user.ID.number.jpg)
            filename = os.path.split(image_path)[1]
            print(f"  Filename: {filename}")
            
            # Split filename to get ID
            parts = filename.split('.')
            print(f"  Filename parts: {parts}")
            
            if len(parts) < 3:
                print(f"  ✗ Invalid filename format: {filename}")
                continue
                
            try:
                id = int(parts[1])  # Should be the ID part
                print(f"  ✓ Extracted ID: {id}")
            except ValueError as e:
                print(f"  ✗ Cannot extract ID from filename {filename}: {e}")
                continue
            
            # Check image dimensions
            print(f"  Image shape: {imageNp.shape}")
            
            faces.append(imageNp)
            ids.append(id)
            
            # Show first few images for verification
            if i < 3:
                cv2.imshow(f"Training Image {i+1}", imageNp)
                cv2.waitKey(500)  # Show for 500ms
            
        except Exception as e:
            print(f"  ✗ Error processing {image_path}: {e}")
            continue
    
    cv2.destroyAllWindows()
    
    if len(faces) == 0:
        print("✗ No valid face images found")
        return False
    
    print(f"✓ Successfully processed {len(faces)} images")
    print(f"✓ Unique IDs found: {set(ids)}")
    
    # Convert ids to numpy array
    ids = np.array(ids)
    print(f"✓ IDs array shape: {ids.shape}")
    
    # Check if OpenCV face module is available
    try:
        print("Testing OpenCV face recognizer...")
        clf = cv2.face.LBPHFaceRecognizer_create()
        print("✓ LBPH Face Recognizer created successfully")
    except AttributeError as e:
        print(f"✗ OpenCV face module not available: {e}")
        print("  This usually means opencv-contrib-python is not installed")
        return False
    except Exception as e:
        print(f"✗ Error creating face recognizer: {e}")
        return False
    
    # Try training
    try:
        print("Starting training...")
        clf.train(faces, ids)
        print("✓ Training completed successfully")
    except Exception as e:
        print(f"✗ Error during training: {e}")
        return False
    
    # Try saving the classifier
    try:
        clf.write("classifier.xml")
        print("✓ Classifier saved to classifier.xml")
        
        # Check if file was created
        if os.path.exists("classifier.xml"):
            file_size = os.path.getsize("classifier.xml")
            print(f"✓ Classifier file size: {file_size} bytes")
        else:
            print("✗ Classifier file was not created")
            return False
            
    except Exception as e:
        print(f"✗ Error saving classifier: {e}")
        return False
    
    print("✓ Training function test completed successfully!")
    return True

def check_opencv_installation():
    """Check OpenCV installation and face module"""
    print("Checking OpenCV installation...")
    
    try:
        print(f"OpenCV version: {cv2.__version__}")
    except:
        print("✗ OpenCV not installed")
        return False
    
    # Check if face module is available
    try:
        recognizer = cv2.face.LBPHFaceRecognizer_create()
        print("✓ OpenCV face module is available")
        return True
    except AttributeError:
        print("✗ OpenCV face module not available")
        print("  You need to install opencv-contrib-python")
        print("  Run: pip install opencv-contrib-python")
        return False
    except Exception as e:
        print(f"✗ Error with face module: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    check_opencv_installation()
    print("=" * 50)
    test_training_function()
    print("=" * 50)
