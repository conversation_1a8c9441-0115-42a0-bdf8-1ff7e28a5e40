import cv2
import numpy as np

def list_cameras():
    """List all available cameras"""
    print("Scanning for available cameras...")
    available_cameras = []
    
    for i in range(10):  # Check first 10 camera indices
        cap = cv2.VideoCapture(i)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                available_cameras.append(i)
                print(f"Camera {i}: Available and working")
            else:
                print(f"Camera {i}: Available but cannot read frames")
            cap.release()
        else:
            print(f"Camera {i}: Not available")
    
    return available_cameras

def test_camera_with_different_methods(camera_index=0):
    """Test camera with different initialization methods"""
    print(f"\nTesting camera {camera_index} with different methods...")
    
    methods = [
        ("Default", lambda: cv2.VideoCapture(camera_index)),
        ("DSHOW", lambda: cv2.VideoCapture(camera_index, cv2.CAP_DSHOW)),
        ("MSMF", lambda: cv2.VideoCapture(camera_index, cv2.CAP_MSMF)),
        ("V4L2", lambda: cv2.VideoCapture(camera_index, cv2.CAP_V4L2)),
        ("GSTREAMER", lambda: cv2.VideoCapture(camera_index, cv2.CAP_GSTREAMER)),
    ]
    
    for method_name, method_func in methods:
        try:
            print(f"\nTrying {method_name}...")
            cap = method_func()
            
            if cap.isOpened():
                print(f"  ✓ Camera opened with {method_name}")
                
                # Try to read a frame
                ret, frame = cap.read()
                if ret:
                    print(f"  ✓ Frame read successfully with {method_name}")
                    print(f"  Frame shape: {frame.shape}")
                    
                    # Test face detection
                    face_classifier = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
                    if not face_classifier.empty():
                        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                        faces = face_classifier.detectMultiScale(gray, 1.3, 5)
                        print(f"  ✓ Face detection test: {len(faces)} faces found")
                    
                    cap.release()
                    return method_name, True
                else:
                    print(f"  ✗ Could not read frame with {method_name}")
            else:
                print(f"  ✗ Could not open camera with {method_name}")
            
            cap.release()
            
        except Exception as e:
            print(f"  ✗ Error with {method_name}: {e}")
    
    return None, False

def check_system_info():
    """Check system and OpenCV info"""
    print("\nSystem Information:")
    print(f"OpenCV version: {cv2.__version__}")
    print(f"Available backends: {[cv2.videoio_registry.getBackendName(b) for b in cv2.videoio_registry.getBackends()]}")

if __name__ == "__main__":
    check_system_info()
    available_cameras = list_cameras()
    
    if available_cameras:
        print(f"\nFound {len(available_cameras)} working cameras: {available_cameras}")
        for cam_idx in available_cameras:
            working_method, success = test_camera_with_different_methods(cam_idx)
            if success:
                print(f"\n🎉 SUCCESS: Camera {cam_idx} works with {working_method} method!")
                break
    else:
        print("\n❌ No working cameras found")
        print("\nTroubleshooting suggestions:")
        print("1. Check if camera is connected properly")
        print("2. Check if camera is being used by another application")
        print("3. Try updating camera drivers")
        print("4. Check Windows Camera privacy settings")
        print("5. Try running as administrator")
